<template>
  <a-modal
    v-model:visible="visible"
    :title="`回复售后-订单号${afterSalesData.orderNumber || ''}-售后编号${afterSalesData.afterSalesNumber || ''}`"
    width="1200px"
    :footer="false"
    @cancel="handleCancel"
  >
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <a-spin size="large" tip="正在加载售后数据...">
        <div style="height: 400px;"></div>
      </a-spin>
    </div>

    <div v-else class="reply-modal-content">
      <!-- 顶部基本信息 -->
      <div class="basic-info-section">
        <div class="info-row">
          <div class="info-item" v-if="afterSalesDetail.followerName || afterSalesData.followerName">
            <span class="label">跟单员：</span>
            <span>{{ afterSalesDetail.followerName || afterSalesData.followerName }}</span>
          </div>
          <div class="info-item" v-if="afterSalesDetail.purchaserName || afterSalesData.purchaserName">
            <span class="label">采购员：</span>
            <span>{{ afterSalesDetail.purchaserName || afterSalesData.purchaserName }}</span>
          </div>
          <div class="info-item" v-if="afterSalesDetail.afterSalesStaffName || afterSalesData.afterSalesStaffName">
            <span class="label">售后员：</span>
            <span>{{ afterSalesDetail.afterSalesStaffName || afterSalesData.afterSalesStaffName }}</span>
          </div>
          <div class="info-item" v-if="afterSalesDetail.applicantName || afterSalesData.applicantName">
            <span class="label">提出人：</span>
            <span>{{ afterSalesDetail.applicantName || afterSalesData.applicantName }}</span>
          </div>
          <div class="info-item" v-if="currentUser">
            <span class="label">回复者：</span>
            <span>{{ currentUser }}</span>
          </div>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 左侧：售后问题 -->
        <div class="left-section">
          <h3 class="section-title">售后问题</h3>

          <div class="form-group" v-if="afterSalesDetail.afterSalesType || afterSalesData.afterSalesType">
            <label class="form-label">售后类型：</label>
            <span class="form-value">{{ afterSalesDetail.afterSalesType || afterSalesData.afterSalesType }}</span>
          </div>

          <div class="form-group" v-if="afterSalesDetail.afterSalesContent || afterSalesData.afterSalesContent">
            <label class="form-label">售后内容：</label>
            <span class="form-value">{{ afterSalesDetail.afterSalesContent || afterSalesData.afterSalesContent }}</span>
          </div>

          <div class="form-group" v-if="afterSalesDetail.customerDemand || afterSalesData.customerDemand">
            <label class="form-label">客户诉求：</label>
            <span class="form-value">{{ afterSalesDetail.customerDemand || afterSalesData.customerDemand }}</span>
          </div>

          <div class="form-group" v-if="(afterSalesDetail.attachments && afterSalesDetail.attachments.length > 0) || (afterSalesData.attachments && afterSalesData.attachments.length > 0)">
            <label class="form-label">附件：</label>
            <span class="form-value">
              <a-button type="text" size="mini" @click="handleViewAttachments" class="attachment-btn">
                <template #icon><icon-file /></template>
                {{ (afterSalesDetail.attachments || afterSalesData.attachments)?.length }}个文件
              </a-button>
            </span>
          </div>

          <div class="form-group">
            <label class="form-label">售后商品：</label>
            <div class="product-table">
              <table>
                <thead>
                  <tr>
                    <th>商品编码</th>
                    <th>商品名称</th>
                    <th>退货数量</th>
                    <th>售后原因</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-if="afterSalesDetail.products && afterSalesDetail.products.length > 0"
                      v-for="product in afterSalesDetail.products" :key="product.productCode">
                    <td>{{ product.productCode }}</td>
                    <td>{{ product.productName }}</td>
                    <td>{{ product.quantity }}</td>
                    <td>{{ product.isReturn ? '是' : '否' }}</td>
                  </tr>
                  <tr v-else-if="afterSalesData.products && afterSalesData.products.length > 0"
                      v-for="product in afterSalesData.products" :key="product.code || product.productCode">
                    <td>{{ product.code || product.productCode }}</td>
                    <td>{{ product.name || product.productName }}</td>
                    <td>{{ product.quantity }}</td>
                    <td>{{ product.isReturn ? '是' : '否' }}</td>
                  </tr>
                  <tr v-else>
                    <td colspan="4" style="text-align: center; color: #999;">暂无商品数据</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- 右侧：售后回复 -->
        <div class="right-section">
          <h3 class="section-title">售后回复</h3>

          <a-form
            :model="formData"
            layout="vertical"
          >
            <a-form-item label="回复内容">
              <a-textarea
                v-model="formData.replyContent"
                placeholder="请输入您的回复内容"
                :rows="4"
              />
            </a-form-item>

            <a-form-item label="责任归属">
              <a-select
                v-model="formData.responsibility"
                placeholder="请选择责任归属"
              >
                <a-option
                  v-for="option in getResponsibilityOptions()"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </a-option>
              </a-select>
            </a-form-item>

            <a-form-item label="公司承担售后金额">
              <a-input-number
                v-model="formData.companyCostAmount"
                placeholder="请输入公司承担售后金额"
              />
              <div class="amount-select">
                <span class="amount-label">我司售后费用：</span>
                <a-select
                  v-model="formData.companyCostType"
                  placeholder="请选择费用类型"
                  style="width: 150px;"
                >
                  <a-option
                    v-for="option in getCostTypeOptions()"
                    :key="option.value"
                    :value="option.value"
                  >
                    {{ option.label }}
                  </a-option>
                </a-select>
              </div>
            </a-form-item>

            <a-form-item label="供应商承担售后金额">
              <a-input-number
                v-model="formData.supplierCostAmount"
                placeholder="请输入供应商承担售后金额"
              />
              <div class="amount-select">
                <span class="amount-label">供应商售后费用：</span>
                <a-select
                  v-model="formData.supplierCostType"
                  placeholder="请选择费用类型"
                  style="width: 150px;"
                >
                  <a-option
                    v-for="option in getCostTypeOptions()"
                    :key="option.value"
                    :value="option.value"
                  >
                    {{ option.label }}
                  </a-option>
                </a-select>
              </div>
            </a-form-item>

            <a-form-item label="售后总费用" field="totalAmount">
              <span class="total-amount">{{ computedTotalAmount }}</span>
            </a-form-item>

            <a-form-item label="是否退货">
              <a-radio-group v-model="formData.isReturn">
                <a-radio :value="true">是</a-radio>
                <a-radio :value="false">否</a-radio>
              </a-radio-group>
            </a-form-item>

            <a-form-item label="回复附件">
              <ma-upload
                v-model="formData.replyAttachments"
                type="file"
                :multiple="true"
                :limit="10"
                accept=".jpg,.jpeg,.png,.pdf,.doc,.docx"
                :draggable="true"
                :size="10 * 1024 * 1024"
                tip="支持格式：jpg、png、pdf、doc、docx等，单个文件不超过10MB"
                return-type="url"
                :request-data="{
                  dir: 'after-sales',
                  module: 'supply',
                  bizType: 'after-sales-reply',
                  isPublic: 'true'
                }"
              />
            </a-form-item>
          </a-form>
        </div>
      </div>

      <!-- 底部按钮 -->
      <div class="footer-actions">
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" :loading="submitLoading" @click="handleSubmit">提交</a-button>
      </div>
    </div>
  </a-modal>

  <!-- 附件查看弹窗 -->
  <a-modal v-model:visible="attachmentModalVisible" title="查看附件" :width="600" :footer="false">
    <div class="attachment-list">
      <div v-for="(attachment, index) in currentAttachments" :key="index" class="attachment-item">
        <div class="attachment-info">
          <icon-file class="file-icon" />
          <span class="file-name">{{ getFileName(attachment) }}</span>
          <span class="file-size">{{ getFileSize(attachment) }}</span>
        </div>
        <div class="attachment-actions">
          <a-button type="text" size="small" @click="handleViewAttachment(attachment)">
            <template #icon><icon-eye /></template>
            查看
          </a-button>
          <a-button type="text" size="small" @click="handleDownloadAttachment(attachment)">
            <template #icon><icon-download /></template>
            下载
          </a-button>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { Message } from '@arco-design/web-vue'
import { IconFile, IconEye, IconDownload } from '@arco-design/web-vue/es/icon'
import MaUpload from '@/components/base/ma-upload/index.vue'

// 状态映射配置
const responsibilityMappings = {
  6: '客户问题',
  1: '供应商问题',
  2: '物流问题',
  3: '销售员问题',
  4: '采购问题',
  5: '产品问题'
}

const costTypeMappings = {
  0: '快递费',
  1: '安装费',
  2: '其他费用'
}

// 获取责任归属选项
const getResponsibilityOptions = () => {
  return Object.entries(responsibilityMappings).map(([value, label]) => ({
    value: parseInt(value),
    label
  }))
}

// 获取费用类型选项
const getCostTypeOptions = () => {
  return Object.entries(costTypeMappings).map(([value, label]) => ({
    value: parseInt(value),
    label
  }))
}

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  afterSalesData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'submit'])

const visible = ref(false)
const submitLoading = ref(false)
const loading = ref(false)
const currentUser = ref('当前用户') // 这里应该从用户状态中获取
const afterSalesDetail = ref({})

// 附件查看相关状态
const attachmentModalVisible = ref(false)
const currentAttachments = ref([])

// 表单数据
const formData = reactive({
  replyContent: '',
  responsibility: null, // 责任归属（数字）
  companyCostType: null, // 我司售后费用类型（数字）
  companyCostAmount: '', // 我司承担费用金额
  supplierCostType: null, // 供应商售后费用类型（数字）
  supplierCostAmount: '', // 供应商承担费用金额
  isReturn: false, // 是否退货
  replyAttachments: []
})

// 计算总费用
const computedTotalAmount = computed(() => {
  const companyAmount = parseFloat(formData.companyCostAmount) || 0
  const supplierAmount = parseFloat(formData.supplierCostAmount) || 0
  return (companyAmount + supplierAmount).toFixed(2)
})

// 自定义校验函数
const validateForm = () => {
  const errors = []

  // 校验回复内容
  if (!formData.replyContent || formData.replyContent.trim() === '') {
    errors.push('请输入回复内容')
  } else if (formData.replyContent.trim().length < 5) {
    errors.push('回复内容至少需要5个字符')
  } else if (formData.replyContent.trim().length > 2000) {
    errors.push('回复内容不能超过2000个字符')
  }

  // 校验费用金额格式
  if (formData.companyCostAmount && formData.companyCostAmount !== '') {
    const amount = parseFloat(formData.companyCostAmount)
    if (isNaN(amount)) {
      errors.push('我司承担费用金额必须是数字')
    } else if (amount < 0) {
      errors.push('我司承担费用金额不能为负数')
    } else if (amount > 999999.99) {
      errors.push('我司承担费用金额不能超过999999.99')
    }
  }

  if (formData.supplierCostAmount && formData.supplierCostAmount !== '') {
    const amount = parseFloat(formData.supplierCostAmount)
    if (isNaN(amount)) {
      errors.push('供应商承担费用金额必须是数字')
    } else if (amount < 0) {
      errors.push('供应商承担费用金额不能为负数')
    } else if (amount > 999999.99) {
      errors.push('供应商承担费用金额不能超过999999.99')
    }
  }

  // 校验费用类型和金额的匹配
  // 1. 公司承担售后金额如果用户输入金额有值，则我司售后费用项则为必填校验
  if (formData.companyCostAmount && formData.companyCostAmount !== '') {
    if (formData.companyCostType === null || formData.companyCostType === undefined) {
      errors.push('公司承担售后金额有值时，我司售后费用项为必填')
    }
  }

  // 2. 供应商承担售后金额如果用户输入金额有值，则供应商售后费用项则为必填校验
  if (formData.supplierCostAmount && formData.supplierCostAmount !== '') {
    if (formData.supplierCostType === null || formData.supplierCostType === undefined) {
      errors.push('供应商承担售后金额有值时，供应商售后费用项为必填')
    }
  }

  // 反向校验：选择了费用类型但没有填写金额
  if (formData.companyCostType !== null && formData.companyCostType !== undefined &&
      (!formData.companyCostAmount || formData.companyCostAmount === '')) {
    errors.push('选择我司售后费用类型时，请填写费用金额')
  }

  if (formData.supplierCostType !== null && formData.supplierCostType !== undefined &&
      (!formData.supplierCostAmount || formData.supplierCostAmount === '')) {
    errors.push('选择供应商售后费用类型时，请填写费用金额')
  }

  // 校验责任归属的合理性
  if (formData.responsibility !== null && formData.responsibility !== undefined) {
    if (![6, 1, 2, 3, 4, 5].includes(formData.responsibility)) {
      errors.push('责任归属选择无效')
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

// 加载回复组件所需数据
const loadAfterSalesDetail = async () => {
  if (!props.afterSalesData?.id) return

  try {
    loading.value = true
    const { afterSalesApi } = await import('@/api/master/csm/afterSales.js')
    const response = await afterSalesApi.getReplyComponentData(props.afterSalesData.id)

    if (response.code === 200) {
      afterSalesDetail.value = response.data
      console.log('回复组件数据加载成功:', afterSalesDetail.value)
    } else {
      Message.error(response.message || '加载回复组件数据失败')
    }
  } catch (error) {
    console.error('加载回复组件数据失败:', error)
    Message.error('加载回复组件数据失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 监听弹窗显示状态
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val && props.afterSalesData && typeof props.afterSalesData === 'object') {
    // 重置表单数据
    resetFormData()
    // 加载售后详情
    loadAfterSalesDetail()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 重置表单数据
const resetFormData = () => {
  formData.replyContent = ''
  formData.responsibility = null
  formData.companyCostType = null
  formData.companyCostAmount = ''
  formData.supplierCostType = null
  formData.supplierCostAmount = ''
  formData.isReturn = false
  formData.replyAttachments = []
}

// 处理附件查看
const handleViewAttachments = () => {
  console.log('查看售后问题附件')

  // 获取附件数据
  const attachments = afterSalesDetail.value.attachments || props.afterSalesData.attachments || []
  currentAttachments.value = attachments
  attachmentModalVisible.value = true
}

// 获取文件名
const getFileName = (attachment) => {
  if (typeof attachment === 'string') {
    return attachment.split('/').pop() || attachment
  }
  return attachment.fileName || attachment.name || '未知文件'
}

// 获取文件大小
const getFileSize = (attachment) => {
  if (typeof attachment === 'object' && attachment.fileSize) {
    const size = parseInt(attachment.fileSize)
    if (isNaN(size) || size === 0) return ''
    if (size < 1024) return `${size}B`
    if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}KB`
    return `${(size / (1024 * 1024)).toFixed(1)}MB`
  }
  return ''
}

// 处理单个附件查看（在新窗口打开）
const handleViewAttachment = (attachment) => {
  console.log('查看附件:', attachment)

  let url = ''

  if (typeof attachment === 'string') {
    url = attachment
  } else {
    // 适配新的附件格式
    url = attachment.fileUrl || attachment.url || attachment.path || ''
  }

  if (url) {
    try {
      // 在新窗口打开附件
      window.open(url, '_blank')
      Message.success('正在打开附件')
    } catch (error) {
      console.error('打开附件失败:', error)
      Message.error('打开附件失败')
    }
  } else {
    Message.error('附件链接无效')
  }
}

// 处理附件下载
const handleDownloadAttachment = async (attachment) => {
  console.log('下载附件:', attachment)

  let url = ''
  let fileName = ''

  if (typeof attachment === 'string') {
    url = attachment
    fileName = attachment.split('/').pop() || 'download'
  } else {
    // 适配新的附件格式
    url = attachment.fileUrl || attachment.url || attachment.path || ''
    fileName = attachment.fileName || attachment.name || 'download'
  }

  if (!url) {
    Message.error('无法下载此文件：文件地址不存在')
    return
  }

  try {
    // 创建一个临时的a标签来触发下载
    const link = document.createElement('a')
    link.href = url
    link.download = fileName
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    Message.success('开始下载文件')
  } catch (error) {
    console.error('下载失败:', error)
    Message.error('下载失败')
  }
}



// 提交处理
const handleSubmit = async () => {
  try {
    // 使用自定义校验
    const validation = validateForm()
    if (!validation.isValid) {
      // 只显示第一个校验错误，避免信息过多
      Message.error(validation.errors[0])
      return
    }

    submitLoading.value = true

    const submitData = {
      replyContent: formData.replyContent,
      responsibility: formData.responsibility,
      companyCostType: formData.companyCostType,
      companyCostAmount: formData.companyCostAmount,
      supplierCostType: formData.supplierCostType,
      supplierCostAmount: formData.supplierCostAmount,
      totalCostAmount: computedTotalAmount.value,
      isReturn: formData.isReturn,
      attachments: Array.isArray(formData.replyAttachments) ? formData.replyAttachments : (formData.replyAttachments ? [formData.replyAttachments] : []),
      afterSalesId: props.afterSalesData.id,
      orderNumber: props.afterSalesData.orderNumber,
      afterSalesNumber: props.afterSalesData.afterSalesNumber,
      submitTime: new Date().toLocaleString(),
      submitter: currentUser.value
    }

    // 调用真实的API接口
    const { afterSalesApi } = await import('@/api/master/csm/afterSales.js')
    const response = await afterSalesApi.addAfterSalesReply(props.afterSalesData.id, submitData)

    if (response.code !== 200) {
      throw new Error(response.message || '回复提交失败')
    }

    emit('submit', submitData)

    Message.success('回复提交成功')
    handleCancel()
  } catch (error) {
    console.log('表单验证失败:', error)
    Message.error('请检查表单内容')
  } finally {
    submitLoading.value = false
  }
}

// 取消处理
const handleCancel = () => {
  visible.value = false
  resetFormData()
}
</script>

<style scoped>
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.reply-modal-content {
  padding: 0;
}

.basic-info-section {
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 20px;
}

.info-row {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
}

.info-item {
  display: flex;
  align-items: center;
  min-width: 200px;
}

.info-item .label {
  font-weight: 500;
  color: #666;
  margin-right: 8px;
  min-width: 80px;
}

.main-content {
  display: flex;
  gap: 24px;
  margin-bottom: 20px;
}

.left-section,
.right-section {
  flex: 1;
  background-color: #fff;
  border: 1px solid #e5e6eb;
  border-radius: 6px;
  padding: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #e5e6eb;
}

.form-group {
  margin-bottom: 12px;
  display: flex;
  align-items: flex-start;
}

.form-label {
  font-weight: 500;
  color: #666;
  min-width: 80px;
  margin-right: 12px;
  line-height: 1.5;
}

.form-value {
  color: #333;
  line-height: 1.5;
  flex: 1;
}

.product-table {
  width: 100%;
  margin-top: 8px;
}

.product-table table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #e5e6eb;
}

.product-table th,
.product-table td {
  padding: 8px 12px;
  text-align: left;
  border: 1px solid #e5e6eb;
  font-size: 12px;
}

.product-table th {
  background-color: #f8f9fa;
  font-weight: 500;
  color: #666;
}

.product-table td {
  color: #333;
}

.amount-select {
  display: flex;
  align-items: center;
  margin-top: 8px;
  gap: 8px;
}

.amount-label {
  font-size: 14px;
  color: #666;
  min-width: 120px;
}

.total-amount {
  font-size: 16px;
  font-weight: 600;
  color: #f53f3f;
}

.upload-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  line-height: 1.4;
}

.footer-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #e5e6eb;
}

:deep(.arco-form-item-label) {
  font-weight: 500;
  color: #333;
}

:deep(.arco-modal-header) {
  border-bottom: 1px solid #e5e6eb;
}

:deep(.arco-modal-body) {
  padding: 20px;
}

/* 附件相关样式 */
.attachment-btn {
  color: #165dff;
  padding: 2px 6px;
}

.attachment-btn:hover {
  background-color: #f2f3f5;
}

.attachment-list {
  max-height: 400px;
  overflow-y: auto;
}

.attachment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #e5e6eb;
  border-radius: 6px;
  margin-bottom: 8px;
  background-color: #fafafa;
}

.attachment-item:last-child {
  margin-bottom: 0;
}

.attachment-info {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.file-icon {
  color: #165dff;
  font-size: 16px;
  margin-right: 8px;
  flex-shrink: 0;
}

.file-name {
  font-weight: 500;
  color: #333;
  margin-right: 8px;
  word-break: break-all;
  flex: 1;
}

.file-size {
  color: #999;
  font-size: 12px;
  margin-right: 12px;
  flex-shrink: 0;
}

.attachment-actions {
  flex-shrink: 0;
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .main-content {
    flex-direction: column;
  }

  .info-row {
    flex-direction: column;
    gap: 12px;
  }

  .info-item {
    min-width: auto;
  }
}
</style>
